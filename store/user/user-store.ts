import { create } from "zustand";
import { persist, createJSONStorage, StorageValue } from "zustand/middleware";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { deleteFile } from "@/utils/media/file-utils";
import { Platform } from "react-native";
import { healthGoalOptions } from "@/constants/health-goals";
import {
  UserProfile,
  NotificationSettings,
  PrivacySettings,
  EmergencyContact,
  Relapse,
  MoodEntry,
} from "@/types/user";
import { HealthMetric } from "@/types/health";
import { MindfulnessExercise, CompletedExercise } from "@/types/mindfulness";
import { Document, MediaFile } from "@/types/media";
// Types are now imported from @/types/* files
interface UserState {
  profile: UserProfile | null;
  isLoading: boolean;
  setProfile: (profile: UserProfile) => void;
  updateProfile: (updates: Partial<UserProfile>) => void;
  addEmergencyContact: (contact: Omit<EmergencyContact, "id">) => void;
  editEmergencyContact: (
    id: string,
    updates: Partial<Omit<EmergencyContact, "id">>
  ) => void;
  removeEmergencyContact: (id: string) => void;
  addRelapse: (relapse: Omit<Relapse, "id">) => void;
  editRelapse: (id: string, updates: Omit<Relapse, "id">) => void;
  removeRelapse: (id: string) => void;
  addMoodEntry: (entry: Omit<MoodEntry, "id">) => void;
  editMoodEntry: (id: string, updates: Omit<MoodEntry, "id">) => void;
  removeMoodEntry: (id: string) => void;
  addDocument: (document: Omit<Document, "id">) => void;
  editDocument: (id: string, updates: Partial<Omit<Document, "id">>) => void;
  removeDocument: (id: string) => void;
  addMediaFile: (mediaFile: Omit<MediaFile, "id">) => Promise<string | null>;
  editMediaFile: (id: string, updates: Partial<Omit<MediaFile, "id">>) => void;
  removeMediaFile: (id: string) => void;
  addCustomExercise: (
    exercise: Omit<MindfulnessExercise, "id" | "isCustom" | "createdAt">
  ) => void;
  editCustomExercise: (
    id: string,
    updates: Partial<Omit<MindfulnessExercise, "id" | "isCustom" | "createdAt">>
  ) => void;
  removeCustomExercise: (id: string) => void;
  toggleFavoriteExercise: (id: string) => void;
  addCompletedExercise: (exercise: Omit<CompletedExercise, "id">) => void;
  addHealthMetric: (metric: Omit<HealthMetric, "id">) => void;
  editHealthMetric: (id: string, updates: Omit<HealthMetric, "id">) => void;
  removeHealthMetric: (id: string) => void;
  resetOnboardingStatus: () => void;
  updateNotificationSettings: (settings: Partial<NotificationSettings>) => void;
  updatePrivacySettings: (settings: Partial<PrivacySettings>) => void;
  clearMediaFiles: () => void;
  exportUserData: () => Promise<Partial<UserProfile>>;
  importUserData: (data: Partial<UserProfile>) => Promise<void>;
}
// Web-specific storage for large media files
const webMediaStorage = new Map<string, string>();
// Helper function to limit the number of media files to prevent storage quota issues
const limitMediaFiles = (
  mediaFiles: MediaFile[],
  newFile: MediaFile
): MediaFile[] => {
  // Keep only the most recent files to prevent storage quota issues
  // Reduced from 20 to 10 for web to prevent quota issues
  const MAX_MEDIA_FILES = Platform.OS === "web" ? 5 : 15;
  // Add the new file
  const updatedFiles = [...mediaFiles, newFile];
  // If we're over the limit, sort by date (newest first) and slice
  if (updatedFiles.length > MAX_MEDIA_FILES) {
    return updatedFiles
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
      .slice(0, MAX_MEDIA_FILES);
  }
  return updatedFiles;
};
// Helper function to validate imported data
const validateImportedData = (data: Partial<UserProfile>): boolean => {
  // Basic validation to ensure the data has the expected structure
  if (!data || typeof data !== "object") return false;
  // Check for required fields
  const requiredFields = ["name", "addiction", "language"];
  for (const field of requiredFields) {
    if (!(field in data)) return false;
  }
  // Validate language field
  if (data.language && !["en", "nl"].includes(data.language)) return false;
  // Validate currency field
  if (data.currency && !["USD", "EUR"].includes(data.currency)) return false;
  // Validate dashboardView field
  if (data.dashboardView && !["cards", "circular"].includes(data.dashboardView))
    return false;
  return true;
};
// Default profile template for new users or imports
const createDefaultProfile = (data: Partial<UserProfile>): UserProfile => {
  return {
    name: data.name || "User",
    addiction: data.addiction || "Other",
    substanceType: data.substanceType || data.addiction || "Other",
    sobrietyDate: data.sobrietyDate || new Date().toISOString(),
    language: data.language || "en",
    usageAmount: data.usageAmount || "Not specified",
    usageQuantity: data.usageQuantity || 0,
    usageUnit: data.usageUnit || "units",
    usageCost: data.usageCost || 0,
    costFrequency: data.costFrequency || "daily",
    currency: data.currency || "USD",
    dashboardView: data.dashboardView || "cards",
    hasCompletedOnboarding: data.hasCompletedOnboarding || false,
    emergencyContacts: data.emergencyContacts || [],
    relapses: data.relapses || [],
    moodEntries: data.moodEntries || [],
    documents: data.documents || [],
    mediaFiles: data.mediaFiles || [],
    customExercises: data.customExercises || [],
    favoriteExercises: data.favoriteExercises || [],
    completedExercises: data.completedExercises || [],
    notificationSettings: data.notificationSettings || {
      dailyReminders: true,
      milestoneAlerts: true,
      emergencyAlerts: true,
      journalReminders: true,
      mindfulnessReminders: true,
      weeklyReports: true,
      communityMessages: false,
    },
    privacySettings: data.privacySettings || {
      dataCollection: true,
      anonymousAnalytics: true,
      crashReporting: true,
      locationTracking: false,
      biometricAuth: false,
      appLock: false,
    },
    profileImage: data.profileImage || null,
    email: data.email || "",
    phone: data.phone || "",
    country: data.country || "",
    birthday: data.birthday || "",
    showHealthMetrics:
      data.showHealthMetrics === undefined ? true : data.showHealthMetrics, // Default to true
    sleepGoal: data.sleepGoal || 7.5, // Default sleep goal to 7.5 hours
    healthMetrics: data.healthMetrics || [], // Initialize healthMetrics
    healthGoals:
      data.healthGoals ||
      healthGoalOptions.map((goal) => {
        // Set sensible default targets based on the goal type
        let defaultTarget = null;
        switch (goal.id) {
          case "sleep":
            defaultTarget = 8;
            break;
          case "exercise":
            defaultTarget = 30;
            break;
          case "pills":
            defaultTarget = 2;
            break;
          case "waterIntake":
            defaultTarget = 8;
            break;
        }
        return {
          ...goal,
          target: defaultTarget,
          enabled: true, // Enable all health goals by default to show all 4 cards
        };
      }),
  };
};
// Helper to sanitize media files for storage to prevent quota issues
const sanitizeMediaFilesForStorage = (mediaFiles: MediaFile[]): MediaFile[] => {
  if (Platform.OS === "web") {
    // For web, we need to be extra careful about storage size
    return mediaFiles.map((file) => {
      // For images, don't store the full URI data in AsyncStorage
      if (file.uri && (file.uri.startsWith("data:") || file.uri.length > 100)) {
        return {
          ...file,
          uri: "[stored separately]", // Just store a placeholder
          thumbnailUri:
            file.thumbnailUri?.startsWith("data:") ||
            (file.thumbnailUri && file.thumbnailUri.length > 100)
              ? "[stored separately]"
              : file.thumbnailUri,
        };
      }
      return {
        id: file.id,
        title: file.title,
        description: file.description,
        date: file.date,
        type: file.type,
        fileName: file.fileName,
        fileType: file.fileType,
        fileSize: file.fileSize,
        category: file.category, // Preserve category field
        // Don't store the full URI in AsyncStorage for web
        uri: "[stored separately]",
        thumbnailUri: "[stored separately]",
      };
    });
  }
  // For native platforms, we can store more data
  return mediaFiles.map((file) => ({
    id: file.id,
    title: file.title,
    description: file.description,
    date: file.date,
    type: file.type,
    fileName: file.fileName,
    fileType: file.fileType,
    uri: file.uri,
    thumbnailUri: file.thumbnailUri,
    fileSize: file.fileSize,
    category: file.category, // Preserve category field
    width: file.width,
    height: file.height,
    duration: file.duration,
  }));
};
// Function to check if we're close to storage quota
const checkStorageQuota = async (): Promise<boolean> => {
  if (Platform.OS !== "web") return true; // Only relevant for web
  try {
    // Check if the Storage API is available and has the estimate method
    if ("storage" in navigator && "estimate" in navigator.storage) {
      const estimate = await navigator.storage.estimate();
      if (estimate.usage && estimate.quota) {
        const percentUsed = (estimate.usage / estimate.quota) * 100;
        console.log(
          `Storage usage: ${percentUsed.toFixed(2)}% (${estimate.usage} of ${
            estimate.quota
          } bytes)`
        );
        // If we're using more than 90% of quota, return false
        if (percentUsed > 90) {
          console.warn("Storage quota is almost full (>90%)");
          return false;
        }
      }
    }
    return true;
  } catch (error) {
    console.error("Error checking storage quota:", error);
    return true; // Assume it's ok if we can't check
  }
};
// Create a custom storage object that handles quota errors
const createQuotaSafeStorage = () => {
  // Create the storage object
  const storage = createJSONStorage<UserState>(() => AsyncStorage);
  return {
    getItem: async (name: string): Promise<StorageValue<UserState> | null> => {
      try {
        // Add null check for storage
        return (await storage?.getItem(name)) || null;
      } catch (error) {
        console.error("Error in getItem:", error);
        return null;
      }
    },
    setItem: async (
      name: string,
      value: StorageValue<UserState>
    ): Promise<void> => {
      try {
        // For web, check quota before attempting to save
        if (Platform.OS === "web") {
          const hasSpace = await checkStorageQuota();
          if (!hasSpace) {
            throw new Error("Storage quota nearly full");
          }
          // Try to save with a smaller value first
          const valueStr =
            typeof value === "string" ? value : JSON.stringify(value);
          if (valueStr.length > 1000000) {
            // If value is larger than ~1MB
            console.warn(
              "Large value detected, attempting to reduce size before saving"
            );
            // Parse the value to modify it
            const parsed = JSON.parse(valueStr) as StorageValue<UserState>;
            // If it has a profile with mediaFiles, sanitize them more aggressively
            if (parsed.state?.profile?.mediaFiles) {
              parsed.state.profile.mediaFiles =
                parsed.state.profile.mediaFiles.map((file: MediaFile) => ({
                  id: file.id,
                  title: file.title,
                  description: file.description
                    ? file.description.substring(0, 100)
                    : "",
                  date: file.date,
                  type: file.type,
                  fileName: file.fileName,
                  fileType: file.fileType,
                  category: file.category, // Preserve category field
                  uri: "[stored separately]",
                  thumbnailUri: "[stored separately]",
                }));
            }
            // Add null check and type assertion
            if (storage) {
              await storage.setItem(name, parsed);
            }
            return;
          }
        }
        // Add null check for storage
        if (storage) {
          await storage.setItem(name, value);
        }
      } catch (error: unknown) {
        console.error(
          "Error in setItem:",
          error instanceof Error ? error.message : String(error)
        );
        // If it's a quota error, try to free up space
        if (
          error instanceof Error &&
          (error.name === "QuotaExceededError" ||
            error.message.includes("quota") ||
            error.message.includes("storage"))
        ) {
          console.warn("Storage quota exceeded, attempting to free space");
          // Try to parse the current value
          try {
            // Add null check for storage
            const currentValue = storage ? await storage.getItem(name) : null;
            if (currentValue) {
              const currentValueStr =
                typeof currentValue === "string"
                  ? currentValue
                  : JSON.stringify(currentValue);
              const parsed = JSON.parse(
                currentValueStr
              ) as StorageValue<UserState>;
              // If it has a profile with mediaFiles, remove some
              if (
                parsed.state?.profile?.mediaFiles &&
                parsed.state.profile.mediaFiles.length > 0
              ) {
                // Keep only the 3 most recent files
                parsed.state.profile.mediaFiles =
                  parsed.state.profile.mediaFiles
                    .sort(
                      (a: MediaFile, b: MediaFile) =>
                        new Date(b.date).getTime() - new Date(a.date).getTime()
                    )
                    .slice(0, 3)
                    .map((file: MediaFile) => ({
                      id: file.id,
                      title: file.title,
                      description: file.description
                        ? file.description.substring(0, 100)
                        : "",
                      date: file.date,
                      type: file.type,
                      fileName: file.fileName,
                      fileType: file.fileType,
                      category: file.category, // Preserve category field
                      uri: "[stored separately]",
                      thumbnailUri: "[stored separately]",
                    }));
                // Add null check and type assertion
                if (storage) {
                  await storage.setItem(name, parsed);
                }
              }
            }
          } catch (innerError) {
            console.error("Error trying to free space:", innerError);
            throw error; // Re-throw the original error
          }
        }
        throw error;
      }
    },
    removeItem: async (name: string): Promise<void> => {
      try {
        // Add null check for storage
        if (storage) {
          await storage.removeItem(name);
        }
      } catch (error) {
        console.error("Error in removeItem:", error);
      }
    },
  };
};
export const useUserStore = create<UserState>()(
  persist(
    (set, get): UserState => ({
      // Explicitly type the return of the state creator
      profile: null,
      isLoading: true,
      setProfile: (profile: UserProfile) => set({ profile, isLoading: false }),
      updateProfile: (updates: Partial<UserProfile>) => {
        console.log("Updating profile with:", updates);
        set((state) => ({
          // state here should now be UserState
          profile: state.profile ? { ...state.profile, ...updates } : null,
        }));
      },
      addEmergencyContact: (contact: Omit<EmergencyContact, "id">) =>
        set((state) => {
          if (!state.profile) return state;
          const newContact: EmergencyContact = {
            ...contact,
            id: Date.now().toString(),
          };
          return {
            profile: {
              ...state.profile,
              emergencyContacts: [
                ...(state.profile.emergencyContacts || []),
                newContact,
              ],
            },
          };
        }),
      editEmergencyContact: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              emergencyContacts: (state.profile.emergencyContacts || []).map(
                (contact) =>
                  contact.id === id ? { ...contact, ...updates, id } : contact
              ),
            },
          };
        }),
      removeEmergencyContact: (id) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              emergencyContacts: (state.profile.emergencyContacts || []).filter(
                (c) => c.id !== id
              ),
            },
          };
        }),
      addRelapse: (relapse) =>
        set((state) => {
          if (!state.profile) return state;
          const newRelapse: Relapse = { ...relapse, id: Date.now().toString() };
          return {
            profile: {
              ...state.profile,
              relapses: [...(state.profile.relapses || []), newRelapse],
            },
          };
        }),
      editRelapse: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              relapses: (state.profile.relapses || []).map((r) =>
                r.id === id ? { ...updates, id } : r
              ),
            },
          };
        }),
      removeRelapse: (id) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              relapses: (state.profile.relapses || []).filter(
                (r) => r.id !== id
              ),
            },
          };
        }),
      addMoodEntry: (entry) =>
        set((state) => {
          if (!state.profile) return state;
          const newEntry: MoodEntry = { ...entry, id: Date.now().toString() };
          return {
            profile: {
              ...state.profile,
              moodEntries: [...(state.profile.moodEntries || []), newEntry],
            },
          };
        }),
      editMoodEntry: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              moodEntries: (state.profile.moodEntries || []).map((e) =>
                e.id === id ? { ...updates, id } : e
              ),
            },
          };
        }),
      removeMoodEntry: (id) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              moodEntries: (state.profile.moodEntries || []).filter(
                (e) => e.id !== id
              ),
            },
          };
        }),
      addDocument: (document) =>
        set((state) => {
          if (!state.profile) return state;
          const newDocument: Document = {
            ...document,
            id: Date.now().toString(),
          };
          const updatedProfile = {
            profile: {
              ...state.profile,
              documents: [...(state.profile.documents || []), newDocument],
            },
          };
          return updatedProfile;
        }),
      editDocument: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              documents: (state.profile.documents || []).map((d) =>
                d.id === id ? { ...d, ...updates, id } : d
              ),
            },
          };
        }),
      removeDocument: (id) =>
        set((state) => {
          if (!state.profile) return state;
          const docToRemove = (state.profile.documents || []).find(
            (d) => d.id === id
          );
          if (docToRemove?.fileUri) {
            deleteFile(docToRemove.fileUri).catch((error) =>
              console.error("Failed to delete document file:", error)
            );
          }
          return {
            profile: {
              ...state.profile,
              documents: (state.profile.documents || []).filter(
                (d) => d.id !== id
              ),
            },
          };
        }),
      addMediaFile: async (mediaFile) => {
        const id = Date.now().toString();
        const newMediaFile: MediaFile = { ...mediaFile, id };
        // For web, store large URI data separately
        if (Platform.OS === "web" && newMediaFile.uri.startsWith("data:")) {
          webMediaStorage.set(id, newMediaFile.uri);
          if (
            newMediaFile.thumbnailUri &&
            newMediaFile.thumbnailUri.startsWith("data:")
          ) {
            webMediaStorage.set(`${id}_thumb`, newMediaFile.thumbnailUri);
          }
        }
        set((state) => {
          if (!state.profile) return state;
          // Limit the number of media files
          const limitedMediaFiles = limitMediaFiles(
            state.profile.mediaFiles || [],
            newMediaFile
          );
          return {
            profile: {
              ...state.profile,
              mediaFiles: limitedMediaFiles,
            },
          };
        });
        return id;
      },
      editMediaFile: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          // Handle web storage for URI if it's being updated
          if (
            Platform.OS === "web" &&
            updates.uri &&
            updates.uri.startsWith("data:")
          ) {
            webMediaStorage.set(id, updates.uri);
            updates.uri = "[stored separately]"; // Update the URI in the profile to placeholder
          }
          if (
            Platform.OS === "web" &&
            updates.thumbnailUri &&
            updates.thumbnailUri.startsWith("data:")
          ) {
            webMediaStorage.set(`${id}_thumb`, updates.thumbnailUri);
            updates.thumbnailUri = "[stored separately]";
          }
          return {
            profile: {
              ...state.profile,
              mediaFiles: (state.profile.mediaFiles || []).map((mf) =>
                mf.id === id ? { ...mf, ...updates, id } : mf
              ),
            },
          };
        }),
      removeMediaFile: (id) =>
        set((state) => {
          if (!state.profile) return state;
          const fileToRemove = (state.profile.mediaFiles || []).find(
            (mf) => mf.id === id
          );
          if (fileToRemove) {
            // Delete from local file system if not web or if URI is not a placeholder
            if (
              Platform.OS !== "web" ||
              (fileToRemove.uri &&
                !fileToRemove.uri.startsWith("[stored separately]"))
            ) {
              deleteFile(fileToRemove.uri).catch((error) =>
                console.error("Failed to delete media file:", error)
              );
            }
            // Also delete thumbnail if it exists and is not a placeholder
            if (
              (fileToRemove.thumbnailUri && Platform.OS !== "web") ||
              (fileToRemove.thumbnailUri &&
                !fileToRemove.thumbnailUri.startsWith("[stored separately]"))
            ) {
              deleteFile(fileToRemove.thumbnailUri).catch((error) =>
                console.error("Failed to delete media thumbnail:", error)
              );
            }
          }
          // Remove from webMediaStorage if on web
          if (Platform.OS === "web") {
            webMediaStorage.delete(id);
            webMediaStorage.delete(`${id}_thumb`);
          }
          return {
            profile: {
              ...state.profile,
              mediaFiles: (state.profile.mediaFiles || []).filter(
                (mf) => mf.id !== id
              ),
            },
          };
        }),
      clearMediaFiles: () =>
        set((state) => {
          if (!state.profile) return state;
          // Delete all files from local storage and webMediaStorage
          (state.profile.mediaFiles || []).forEach((media) => {
            if (
              Platform.OS !== "web" ||
              (media.uri && !media.uri.startsWith("[stored separately]"))
            ) {
              deleteFile(media.uri).catch((error) =>
                console.error("Failed to delete media file:", error)
              );
            }
            if (
              media.thumbnailUri &&
              (Platform.OS !== "web" ||
                (media.thumbnailUri &&
                  !media.thumbnailUri.startsWith("[stored separately]")))
            ) {
              deleteFile(media.thumbnailUri).catch((error) =>
                console.error("Failed to delete media thumbnail:", error)
              );
            }
            if (Platform.OS === "web") {
              webMediaStorage.delete(media.id);
              webMediaStorage.delete(`${media.id}_thumb`);
            }
          });
          return {
            profile: {
              ...state.profile,
              mediaFiles: [],
            },
          };
        }),
      addCustomExercise: (exercise) =>
        set((state) => {
          if (!state.profile) return state;
          const newExercise: MindfulnessExercise = {
            ...exercise,
            id: Date.now().toString(),
            isCustom: true,
            createdAt: new Date().toISOString(),
          };
          return {
            profile: {
              ...state.profile,
              customExercises: [
                ...(state.profile.customExercises || []),
                newExercise,
              ],
            },
          };
        }),
      editCustomExercise: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              customExercises: (state.profile.customExercises || []).map((ex) =>
                ex.id === id ? { ...ex, ...updates, id, isCustom: true } : ex
              ),
            },
          };
        }),
      removeCustomExercise: (id) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              customExercises: (state.profile.customExercises || []).filter(
                (ex) => ex.id !== id
              ),
            },
          };
        }),
      toggleFavoriteExercise: (id) =>
        set((state) => {
          if (!state.profile) return state;
          const favs = state.profile.favoriteExercises || [];
          const isFav = favs.includes(id);
          return {
            profile: {
              ...state.profile,
              favoriteExercises: isFav
                ? favs.filter((favId) => favId !== id)
                : [...favs, id],
            },
          };
        }),
      addCompletedExercise: (exercise) =>
        set((state) => {
          if (!state.profile) return state;
          const newCompleted: CompletedExercise = {
            ...exercise,
            id: Date.now().toString(),
          };
          return {
            profile: {
              ...state.profile,
              completedExercises: [
                ...(state.profile.completedExercises || []),
                newCompleted,
              ],
            },
          };
        }),
      addHealthMetric: (metric) =>
        set((state) => {
          if (!state.profile) return state;
          const newMetric: HealthMetric = {
            ...metric,
            id: Date.now().toString(),
          };

          // Update corresponding health goal history
          const updatedHealthGoals = (state.profile.healthGoals || []).map(goal => {
            // Map goal IDs to metric names
            let metricName = goal.id;
            if (goal.id === "waterIntake") {
              metricName = "hydration";
            }

            if (metricName === metric.metric) {
              const metricValue = typeof metric.value === 'number'
                ? metric.value
                : parseFloat(metric.value.toString()) || 0;

              const existingHistory = goal.history || [];
              const dateString = metric.date;

              // Remove any existing entry for this date and add the new one
              const filteredHistory = existingHistory.filter(entry => entry.date !== dateString);

              return {
                ...goal,
                history: [...filteredHistory, { date: dateString, value: metricValue }]
              };
            }
            return goal;
          });

          return {
            profile: {
              ...state.profile,
              healthMetrics: [
                ...(state.profile.healthMetrics || []),
                newMetric,
              ],
              healthGoals: updatedHealthGoals,
            },
          };
        }),
      editHealthMetric: (id, updates) =>
        set((state) => {
          if (!state.profile) return state;

          const updatedHealthMetrics = (state.profile.healthMetrics || []).map((metric) =>
            metric.id === id ? { ...metric, ...updates, id } : metric
          );

          // Find the updated metric to sync with health goals
          const updatedMetric = updatedHealthMetrics.find(m => m.id === id);

          // Update corresponding health goal history if metric was found
          let updatedHealthGoals = state.profile.healthGoals || [];
          if (updatedMetric) {
            updatedHealthGoals = updatedHealthGoals.map(goal => {
              // Map goal IDs to metric names
              let metricName = goal.id;
              if (goal.id === "waterIntake") {
                metricName = "hydration";
              }

              if (metricName === updatedMetric.metric) {
                const metricValue = typeof updatedMetric.value === 'number'
                  ? updatedMetric.value
                  : parseFloat(updatedMetric.value.toString()) || 0;

                const existingHistory = goal.history || [];
                const dateString = updatedMetric.date;

                // Remove any existing entry for this date and add the updated one
                const filteredHistory = existingHistory.filter(entry => entry.date !== dateString);

                return {
                  ...goal,
                  history: [...filteredHistory, { date: dateString, value: metricValue }]
                };
              }
              return goal;
            });
          }

          return {
            profile: {
              ...state.profile,
              healthMetrics: updatedHealthMetrics,
              healthGoals: updatedHealthGoals,
            },
          };
        }),
      removeHealthMetric: (id) =>
        set((state) => {
          if (!state.profile) return state;

          // Find the metric being removed to sync with health goals
          const metricToRemove = (state.profile.healthMetrics || []).find(m => m.id === id);

          const updatedHealthMetrics = (state.profile.healthMetrics || []).filter(
            (metric) => metric.id !== id
          );

          // Update corresponding health goal history if metric was found
          let updatedHealthGoals = state.profile.healthGoals || [];
          if (metricToRemove) {
            updatedHealthGoals = updatedHealthGoals.map(goal => {
              // Map goal IDs to metric names
              let metricName = goal.id;
              if (goal.id === "waterIntake") {
                metricName = "hydration";
              }

              if (metricName === metricToRemove.metric) {
                const existingHistory = goal.history || [];
                const dateString = metricToRemove.date;

                // Remove the entry for this date
                const filteredHistory = existingHistory.filter(entry => entry.date !== dateString);

                return {
                  ...goal,
                  history: filteredHistory
                };
              }
              return goal;
            });
          }

          return {
            profile: {
              ...state.profile,
              healthMetrics: updatedHealthMetrics,
              healthGoals: updatedHealthGoals,
            },
          };
        }),
      resetOnboardingStatus: () =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              hasCompletedOnboarding: false,
            },
          };
        }),
      updateNotificationSettings: (settings) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              notificationSettings: {
                ...(state.profile.notificationSettings || {
                  dailyReminders: true,
                  milestoneAlerts: true,
                  emergencyAlerts: true,
                  journalReminders: true,
                  mindfulnessReminders: true,
                  weeklyReports: true,
                  communityMessages: false,
                }),
                ...settings,
              },
            },
          };
        }),
      updatePrivacySettings: (settings) =>
        set((state) => {
          if (!state.profile) return state;
          return {
            profile: {
              ...state.profile,
              privacySettings: {
                ...(state.profile.privacySettings || {
                  dataCollection: true,
                  anonymousAnalytics: true,
                  crashReporting: true,
                  locationTracking: false,
                  biometricAuth: false,
                  appLock: false,
                }),
                ...settings,
              },
            },
          };
        }),
      exportUserData: async () => {
        const profile = get().profile;
        if (!profile) return {};
        // For web, retrieve full URIs from webMediaStorage
        const mediaFilesToExport =
          Platform.OS === "web"
            ? (profile.mediaFiles || []).map((file) => ({
                ...file,
                uri: webMediaStorage.get(file.id) || file.uri,
                thumbnailUri:
                  webMediaStorage.get(`${file.id}_thumb`) || file.thumbnailUri,
              }))
            : profile.mediaFiles;
        return {
          ...profile,
          documents: (profile.documents || []).map((doc) => ({
            // Ensure documents are included
            ...doc,
          })),
          mediaFiles: mediaFilesToExport,
        };
      },
      importUserData: async (data) => {
        if (!validateImportedData(data)) {
          throw new Error("Invalid data format for import.");
        }
        const newProfile = createDefaultProfile(data);
        // For web, store imported media URIs in webMediaStorage
        if (Platform.OS === "web" && newProfile.mediaFiles) {
          newProfile.mediaFiles.forEach((file) => {
            if (file.uri && file.uri.startsWith("data:")) {
              webMediaStorage.set(file.id, file.uri);
              file.uri = "[stored separately]";
            }
            if (file.thumbnailUri && file.thumbnailUri.startsWith("data:")) {
              webMediaStorage.set(`${file.id}_thumb`, file.thumbnailUri);
              file.thumbnailUri = "[stored separately]";
            }
          });
        }
        set({ profile: newProfile, isLoading: false });
      },
    }),
    {
      name: "user-profile-storage",
      storage: createQuotaSafeStorage(),
      // Only persist parts of the state that are safe and necessary
      partialize: (state: UserState): UserState => {
        if (!state.profile) {
          // Return the state as is, ensuring all UserState fields are present
          // If profile is null, it remains null. Actions and other fields are returned.
          return {
            ...state,
            profile: null,
          };
        }
        const sanitizedMediaFiles = sanitizeMediaFilesForStorage(
          state.profile.mediaFiles || []
        );
        // Return the full state structure, but with a modified profile
        return {
          ...state, // This includes all actions and other state fields
          profile: {
            ...state.profile, // Spread the original profile
            mediaFiles: sanitizedMediaFiles, // Override with sanitized media files
          },
        };
      },
      // After rehydration, restore full media URIs for web
      // The 'persistedState' here is what's read from storage, effectively UserState with functions removed.
      onRehydrateStorage: (persistedState?: UserState, error?: Error) => {
        if (error) {
          console.error("An error occurred during rehydration:", error);
          return;
        }
        // If persistedState or its critical parts are undefined, nothing to do.
        if (
          !persistedState ||
          !persistedState.profile ||
          !persistedState.profile.mediaFiles
        ) {
          return;
        }
        // Directly modify the persistedState. Zustand will use this modified version
        // to hydrate the store after this callback completes.
        if (Platform.OS === "web") {
          persistedState.profile.mediaFiles =
            persistedState.profile.mediaFiles.map((file) => {
              let restoredUri = file.uri;
              let restoredThumbnailUri = file.thumbnailUri;
              if (
                file.uri === "[stored separately]" &&
                webMediaStorage.has(file.id)
              ) {
                restoredUri = webMediaStorage.get(file.id) || file.uri;
              }
              if (
                file.thumbnailUri === "[stored separately]" &&
                webMediaStorage.has(`${file.id}_thumb`)
              ) {
                restoredThumbnailUri =
                  webMediaStorage.get(`${file.id}_thumb`) || file.thumbnailUri;
              }
              return {
                ...file,
                uri: restoredUri,
                thumbnailUri: restoredThumbnailUri,
              };
            });
        }
      },
    }
  )
);
// Subscribe to hydration completion to set isLoading to false
useUserStore.persist.onFinishHydration((persistedState) => {
  useUserStore.setState({ isLoading: false });
  if (!persistedState?.profile) {
    console.log(
      "No profile found after hydration (first launch or cleared storage). Onboarding should proceed."
    );
  } else {
    console.log("Profile successfully rehydrated.");
    console.log("Rehydrated documents count:", persistedState.profile.documents?.length || 0);
    console.log("Rehydrated media files count:", persistedState.profile.mediaFiles?.length || 0);
  }
});
// Helper to get the actual media URI for web
export const getMediaUri = (
  id: string,
  isThumb = false
): string | undefined => {
  if (Platform.OS === "web") {
    return webMediaStorage.get(isThumb ? `${id}_thumb` : id);
  }
  // For native, the URI is already in the profile, but this function could be expanded
  // if native also starts storing URIs separately for some reason.
  const profile = useUserStore.getState().profile;
  const file = profile?.mediaFiles?.find((f) => f.id === id);
  return isThumb ? file?.thumbnailUri : file?.uri;
};
// Re-export types for backward compatibility
export type {
  UserProfile,
  NotificationSettings,
  PrivacySettings,
  EmergencyContact,
  Relapse,
  MoodEntry,
} from "@/types/user";
export type { HealthMetric, HealthGoal } from "@/types/health";
export type { MindfulnessExercise, CompletedExercise } from "@/types/mindfulness";
export type { Document, MediaFile } from "@/types/media";